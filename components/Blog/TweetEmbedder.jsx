import React, { useEffect } from "react";
import TwitterEmbed from "../TwitterEmbed";

const TweetEmbedder = () => {
  useEffect(() => {
    // Load Twitter widget script
    const loadTwitterScript = () => {
      if (window.twttr) return Promise.resolve();

      return new Promise((resolve) => {
        const script = document.createElement("script");
        script.setAttribute("src", "https://platform.twitter.com/widgets.js");
        script.setAttribute("charset", "utf-8");
        script.setAttribute("async", "true");
        script.onload = resolve;
        script.onerror = () => {
          console.error("Failed to load Twitter widgets script");
          resolve(); // Still resolve to prevent hanging
        };
        document.head.appendChild(script);
      });
    };

    // Function to process blockquotes and social embeds
    const processTweetEmbeds = async () => {
      try {
        // Load Twitter script first
        await loadTwitterScript();

        // Find all blockquotes with numeric content (tweet IDs)
        const blockquotes = document.querySelectorAll("blockquote");
        const processedElements = [];

        blockquotes.forEach((blockquote, index) => {
          const text = blockquote.textContent.trim();

          // Check if the blockquote contains only a numeric tweet ID
          const tweetIdMatch = text.match(/^\d{15,20}$/);

          if (tweetIdMatch) {
            const tweetId = tweetIdMatch[0];

            // Create a container for the Twitter embed
            const embedContainer = document.createElement("div");
            embedContainer.className = "twitter-embed-container my-6";
            embedContainer.setAttribute("data-tweet-id", tweetId);
            embedContainer.setAttribute("data-processed", "true");

            // Replace the blockquote with the embed container
            blockquote.parentNode.replaceChild(embedContainer, blockquote);

            // Create a simple HTML structure for the tweet embed
            // We'll let the TwitterEmbed component handle the actual embedding
            embedContainer.innerHTML = `
              <div class="tweet-placeholder" data-tweet-id="${tweetId}">
                <div class="animate-pulse flex flex-col items-center justify-center p-6 min-h-[200px]">
                  <div class="h-10 bg-gray-200 rounded-full w-10 mb-2"></div>
                  <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div class="mt-4 text-blue-400 text-sm">Loading tweet ${tweetId}...</div>
                </div>
              </div>
            `;

            // Trigger a custom event that can be handled by the parent component
            const embedEvent = new CustomEvent("tweetEmbedFound", {
              detail: { tweetId, container: embedContainer },
            });
            document.dispatchEvent(embedEvent);

            processedElements.push({
              container: embedContainer,
              tweetId,
              originalElement: blockquote,
            });
          }
        });

        // Process Hygraph social embeds
        const socialEmbeds = document.querySelectorAll(
          '[data-embed-type="social"]'
        );

        socialEmbeds.forEach((embed) => {
          const url = embed.getAttribute("data-url");
          const platform = embed.getAttribute("data-platform");

          if (platform === "twitter" && url) {
            // Extract tweet ID from URL
            const tweetIdMatch = url.match(/status\/(\d+)/);

            if (tweetIdMatch) {
              const tweetId = tweetIdMatch[1];

              // Create container for Twitter embed
              const embedContainer = document.createElement("div");
              embedContainer.className = "twitter-embed-container my-6";
              embedContainer.setAttribute("data-tweet-id", tweetId);
              embedContainer.setAttribute("data-processed", "true");

              // Replace the social embed with Twitter embed
              embed.parentNode.replaceChild(embedContainer, embed);

              // Create placeholder for Twitter embed
              embedContainer.innerHTML = `
                <div class="tweet-placeholder" data-tweet-id="${tweetId}">
                  <div class="animate-pulse flex flex-col items-center justify-center p-6 min-h-[200px]">
                    <div class="h-10 bg-gray-200 rounded-full w-10 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div class="mt-4 text-blue-400 text-sm">Loading tweet ${tweetId}...</div>
                  </div>
                </div>
              `;

              // Trigger event for parent component to handle
              const embedEvent = new CustomEvent("tweetEmbedFound", {
                detail: { tweetId, container: embedContainer },
              });
              document.dispatchEvent(embedEvent);

              processedElements.push({
                container: embedContainer,
                tweetId,
                originalElement: embed,
              });
            }
          }
        });

        // Load Twitter widgets for any remaining native Twitter embeds
        if (window.twttr && window.twttr.widgets) {
          try {
            await window.twttr.widgets.load();
          } catch (error) {
            console.error("Error loading Twitter widgets:", error);
          }
        }

        console.log(
          `TweetEmbedder processed ${processedElements.length} tweet embeds`
        );
      } catch (error) {
        console.error("Error processing tweet embeds:", error);
      }
    };

    // Process embeds after DOM is ready
    const timeoutId = setTimeout(() => {
      processTweetEmbeds();
    }, 1000); // Wait 1 second for DOM to be fully loaded

    // Also process on DOM content loaded if not already loaded
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", processTweetEmbeds);
    }

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener("DOMContentLoaded", processTweetEmbeds);
    };
  }, []);

  // This component doesn't render anything visible
  return <div className="tweet-embedder-container hidden" />;
};

export default TweetEmbedder;
